<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>模具维修记录</title>
  <style>
    html, body {
      max-width: 100vw;
      overflow-x: hidden;
    }

    input[type="file"] {
      max-width: 100%;
      box-sizing: border-box;
    }
    
    /* 防止移动端输入框聚焦时页面缩放 */
    @media screen and (max-width: 768px) {
      input, textarea, select {
        font-size: 14px; /* 防止iOS Safari自动缩放 */
        max-width: 100%;
        min-width: 0;
        min-height: 42px;
        transform: translateZ(0); /* 启用硬件加速 */
      }
      
      body {
        padding: 8px;
        margin: 0;
        overflow-x: hidden;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        text-size-adjust: 100%;
      }
      
      .form-group {
        width: 100%;
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
      }
      
      .image-upload-container {
        width: 100%;
        max-width: 100%;
      }
    }
    body {
      font-family: sans-serif;
      padding: 12px;
      background: #f9f9f9;
      width: 100%;
      box-sizing: border-box;
    }
    
    * {
      box-sizing: border-box;
    }
    
    .form-group {
      margin-bottom: 8px;
      width: 100%;
      max-width: 100%;
    }
    label {
      display: block;
      margin-bottom: 6px;
      font-size: 15px;
    }
    input, textarea, select {
      width: 100%;
      padding: 10px;
      font-size: 14px;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 4px;
      min-height: 42px; /* 确保与input高度一致 */
    }
    
    select {
      line-height: 1.4; /* 改善文本垂直对齐 */
    }
    input:focus, select:focus, textarea:focus, button:focus {
      outline: none;
      border-color: #ccc;
      box-shadow: 0 0 3px #ccc;
    }
    textarea {
      resize: vertical;
    }
    .image-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;
    }
    .image-preview img {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      width: 100%;
      padding: 12px;
      background: #409eff;
      color: white;
      font-size: 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
    }
    /* 图片上传区域容器 - 包含上传按钮和所有图片 */
.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  align-items: flex-start;
}

/* 图片预览区域 - 不再需要独立的flex布局 */
.image-preview {
  display: contents;
}

/* 图片容器 */
.image-box {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.image-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 删除按钮 */
.image-box .delete-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  background: red;
  color: white;
  font-size: 12px;
  border: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  cursor: pointer;
  padding: 0;
  z-index: 1;
}
.required {
    color: red;
    margin-right: 4px;
  }
  .hidden-input {
    display: none;
  }
/* 上传按钮样式 - 与图片保持一致的大小 */
.select-btn {
  width: 80px;
  height: 80px;
  padding: 0;
  font-size: 24px;
  box-sizing: border-box;
  border: 2px dashed #ccc;
  border-radius: 4px;
  background: #f9f9f9;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.select-btn:hover {
  border-color: #409eff;
  color: #409eff;
  background: #f0f8ff;
}

.select-btn:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 3px #409eff;
}
  </style>
</head>
<body>
  <!-- <div style="text-align: right;">
  <button
    onclick="window.location.href='list.html'"
    style="padding: 4px; font-size: 14px; background: #409eff; color: white; border: none; border-radius: 4px;width:64px;"
  >
    查看列表
  </button>
</div> -->

  <div class="form-group">
    <label for="productCode"><span class="required">*</span>模具编号</label>
    <input id="productCode" list="mouldList" placeholder="请输入模具编号" required/>
    <datalist id="mouldList">
      <!-- JS 动态填充 -->
    </datalist>
  </div>

 
<div class="form-group">
  <label for="customerName"><span class="required">*</span>客户名称</label>
  <input id="customerName" list="customerList" placeholder="请选择客户名称" required/>
  <datalist id="customerList">
  </datalist>
</div>

  <div class="form-group">
    <label for="productName"><span class="required">*</span>模具名称</label>
    <input id="productName" placeholder="请输入模具名称" required/>

  </div>

   <div class="form-group">
    <label for="statusName">维修状态</label>
    <select id="statusName">
      <option value="待处理">待处理</option>
      <option value="处理中">处理中</option>
      <option value="已暂停">已暂停</option>
      <option value="已取消">已取消</option>
      <option value="已完成">已完成</option>
    </select>
  </div>
  <div class="form-group">
    <label for="afterSaleDate">维修日期</label>
    <input id="afterSaleDate" type="date" placeholder="请选择维修日期"/>
  </div>
  <div class="form-group">
    <label for="repairReason"><span class="required">*</span>维修原因</label>
    <textarea id="repairReason" rows="4" maxlength="500" placeholder="请输入维修原因" required></textarea>
  </div>

  <div class="form-group">
    <label for="repairContent">维修内容</label>
    <textarea id="repairContent" rows="4" maxlength="500" placeholder="请输入维修内容"></textarea>
  </div>

  <div class="form-group">
    <label for="repairVerificationResult">维修验证结果</label>
    <textarea id="repairVerificationResult" rows="4" maxlength="500" placeholder="请输入维修验证结果"></textarea>
  </div>

  <!-- 异常照片 -->
    <div class="form-group">
    <label for="abnormalImages">异常照片 (最多9张)</label>
    <input type="file" id="abnormalImages" accept="image/*" multiple class="hidden-input"/>
    <div class="image-upload-container">
      <button type="button" onclick="document.getElementById('abnormalImages').click()" class="select-btn">
        +
      </button>
      <div class="image-preview" id="abnormalPreview"></div>
    </div>
    </div>

    <!-- 验证照片 -->
    <div class="form-group">
    <label for="verifyImages">验证照片 (最多9张)</label>
    <input type="file" id="verifyImages" accept="image/*" multiple class="hidden-input"/>
    <div class="image-upload-container">
      <button type="button" onclick="document.getElementById('verifyImages').click()" class="select-btn">
        +
      </button>
      <div class="image-preview" id="verifyPreview"></div>
    </div>
    </div>  


  <div class="form-group">
    <label for="remarks">备注</label>
    <textarea id="remarks" rows="4" maxlength="500" placeholder="请输入备注"></textarea>
  </div>

  <button onclick="submitForm()">提交</button>
  <script src="https://crm.superband.com.cn:46443/axios.min.js"></script>
  <script>
    const VUE_APP_BASE_API = 'https://crm.superband.com.cn:46443';
    let editIndex = null;
    const params = new URLSearchParams(window.location.search);
    editIndex = params.get('edit');
    const adminToken = params.get('adminToken');

    function debounce(fn, delay = 300) {
      let timer = null;
      return function (...args) {
        clearTimeout(timer);
        timer = setTimeout(() => fn.apply(this, args), delay);
      };
    }

    // 模具编号搜索函数
    const searchMould = debounce(async function (keyword) {
      const mouldList = document.getElementById('mouldList');
      mouldList.innerHTML = '';

      try {
        const res = await axios.post(`${VUE_APP_BASE_API}/crmProduct/queryPageList`, {
          pageNo: 1,
          pageSize: 1000,
          search: keyword,
          type: 4
        }, { headers: { 'admin-token': adminToken } });

        const list = res.data?.data?.list || [];
        list.forEach(item => {
          const option = document.createElement('option');
          option.value = item.num;
          option.textContent = item.num;
          mouldMap[item.num] = {
            name: item.name,
            num: item.num,
            productId: item.productId
          };
          mouldList.appendChild(option);
        });
      } catch (err) {
        console.error('模具编号搜索失败', err);
      }
    }, 300);

    // 模具编号输入事件
    document.getElementById('productCode').addEventListener('input', function () {
      const keyword = this.value.trim();
      searchMould(keyword);
    });

    // 模具编号聚焦事件
    document.getElementById('productCode').addEventListener('focus', function () {
      const keyword = this.value.trim();
      searchMould(keyword);
    });


  // 监听选中模具编号，自动回填模具名称
  document.getElementById('productCode').addEventListener('change', function () {
    const code = this.value;
    const nameInput = document.getElementById('productName');
    if (mouldMap[code]) {
      nameInput.value = mouldMap[code].name;
    } else {
      nameInput.value = '';
    }
  });

   // 客户名称搜索函数
   const searchCustomer = debounce(async function (keyword) {
     const customerList = document.getElementById('customerList');
     customerList.innerHTML = '';

     try {
       const res = await axios.post(`${VUE_APP_BASE_API}/crmCustomer/queryPageList`, {
         pageNo: 1,
         pageSize: 1000,
         search: keyword,
         type: 2
       }, { headers: { 'admin-token': adminToken } });

       const list = res.data?.data?.list || [];
       list.forEach(item => {
         const option = document.createElement('option');
         option.value = item.customerName; // 显示客户名称
         option.textContent = item.customerName;
         // 建立客户名称到客户ID的映射
         customerMap[item.customerName] = {
           customerId: item.customerId,
           customerName: item.customerName
         };
         customerList.appendChild(option);
       });
     } catch (err) {
       console.error('客户名称搜索失败', err);
     }
   }, 300);

   // 客户名称输入事件
   document.getElementById('customerName').addEventListener('input', function () {
     const keyword = this.value.trim();
     searchCustomer(keyword);
   });

   // 客户名称聚焦事件
   document.getElementById('customerName').addEventListener('focus', function () {
     const keyword = this.value.trim();
       searchCustomer(keyword);
   });

    document.addEventListener('DOMContentLoaded', () => {
      if (editIndex) {
        axios.post(`${VUE_APP_BASE_API}/crmAfterSale/queryById/${editIndex}`,null,
      {
        headers: { 'admin-token': adminToken }
      }).then((res) => {
              const record  = res.data.data;
              if (record) {
                document.getElementById('productCode').value = record.productCode;
                // 设置客户名称并建立映射关系
                document.getElementById('customerName').value = record.customerName;
                if (record.customerId && record.customerName) {
                  customerMap[record.customerName] = {
                    customerId: record.customerId,
                    customerName: record.customerName
                  };
                }
                document.getElementById('productName').value = record.productName;
                document.getElementById('statusName').value = record.status == 1?'待处理':record.status == 2?'处理中':record.status == 3?'已暂停':record.status == 4?'已取消':record.status == 5?'已完成':'';
                document.getElementById('afterSaleDate').value = record.afterSaleDate.split(' ')[0];
                document.getElementById('repairReason').value = record.repairReason;
                document.getElementById('repairContent').value = record.repairContent;
                document.getElementById('repairVerificationResult').value = record.repairVerificationResult;
                document.getElementById('remarks').value = record.remarks;
                
                // 加载图片数据
                loadExistingImages(record.abnormalPhotoList, 'abnormalImages', 'abnormalPreview');
                loadExistingImages(record.verificationPhotoList, 'verifyImages', 'verifyPreview');
              }
            })  
          }
        });

    const customerMap = {};
    const mouldMap = {};

    // 全局存储图片数组
    const imageState = {
      abnormalImages: [],
      verifyImages: []
    };

    // 加载已存在的图片（编辑模式）
    function loadExistingImages(images, key, previewId) {
        if (!images || !Array.isArray(images)) return;
        
        const preview = document.getElementById(previewId);
        
        images.forEach(imageData => {
            // 创建图片容器
            const box = document.createElement('div');
            box.className = 'image-box';

            const img = document.createElement('img');
            // 使用现有的图片路径或构建新的路径
            img.src = `${VUE_APP_BASE_API}/adminFile/downImg/${imageData.fileId}`;

            const del = document.createElement('button');
            del.className = 'delete-btn';
            del.innerText = '×';

            const id = imageData.id || Date.now() + Math.random();
            
            // 删除逻辑
            del.onclick = () => {
                preview.removeChild(box);
                imageState[key] = imageState[key].filter(i => i.id !== id);
                console.log(`删除图片后 ${key} 数组长度:`, imageState[key].length);
            };

            // 将图片数据添加到状态中
            const imageStateData = {
                ...imageData
            };
            
            imageState[key].push(imageStateData);
            console.log(`加载已存在图片到 ${key} 数组:`, imageStateData);

            box.appendChild(img);
            box.appendChild(del);
            preview.appendChild(box);
        });
        
        console.log(`${key} 图片加载完成，数组长度:`, imageState[key].length);
    }

    // 上传图片到服务器
    async function uploadImageToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'img');
        formData.append('isOnline', '1');
        formData.append('batchId', '87eb30837434921' + Date.now() + 'bdc4c740d8d3336a');

        try {
            const response = await axios.post(`${VUE_APP_BASE_API}/adminFile/upload`, formData, {
                headers: {
                    'admin-token': adminToken,
                    'Content-Type': 'multipart/form-data'
                }
            });
            
            if (response.data.code === 0) {
                return response.data.data;
            } else {
                throw new Error(response.data.msg || '上传失败');
            }
        } catch (error) {
            console.error('图片上传失败:', error);
            alert('图片上传失败，请重试');
            throw error;
        }
    }

    function handleImagePreview(inputId, previewId, key) {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(previewId);

        input.addEventListener('change', async () => {
            const newFiles = Array.from(input.files);
            const currentCount = imageState[key].length;

            const availableSlots = 9 - currentCount;
            if (availableSlots <= 0) {
                alert('最多上传9张图片');
                return;
            }

            const filesToAdd = newFiles.slice(0, availableSlots);

            // 为每个文件创建上传任务
            for (const file of filesToAdd) {
                let box = null;
                try {
                    // 创建图片容器和加载状态
                    box = document.createElement('div');
                    box.className = 'image-box';

                    const img = document.createElement('img');
                    img.style.opacity = '0.5';
                    img.alt = '上传中...';

                    const del = document.createElement('button');
                    del.className = 'delete-btn';
                    del.innerText = '×';
                    del.disabled = true;

                    // 显示本地预览
                    const reader = new FileReader();
                    reader.onload = e => {
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);

                    box.appendChild(img);
                    box.appendChild(del);
                    preview.appendChild(box);

                    // 上传到服务器
                    const uploadedData = await uploadImageToServer(file);
                    
                    // 构建完整的图片URL
                    uploadedData.url = `${VUE_APP_BASE_API}/adminFile/downImg/${uploadedData.fileId}`;
                    
                    // 上传成功后更新状态
                    img.style.opacity = '1';
                    img.src = uploadedData.url; // 使用服务器返回的图片路径
                    del.disabled = false;

                    const id = Date.now() + Math.random();
                    
                    // 删除逻辑
                    del.onclick = () => {
                        preview.removeChild(box);
                        imageState[key] = imageState[key].filter(i => i.id !== id);
                        console.log(`删除图片后 ${key} 数组长度:`, imageState[key].length);
                    };

                    // 将服务器返回的数据存储到数组中
                    const imageData = {
                        id,
                        ...uploadedData // 包含服务器返回的所有数据
                    };
                    
                    imageState[key].push(imageData);
                    console.log(`添加图片到 ${key} 数组:`, imageData);
                    console.log(`当前 ${key} 数组长度:`, imageState[key].length);

                } catch (error) {
                    console.error('图片上传失败:', error);
                    // 上传失败时移除预览
                    if (box && box.parentNode) {
                        preview.removeChild(box);
                    }
                }
            }

            input.value = ''; // 解决同图不能再次选中问题
        });
    }

    // 表单提交
    function submitForm() {
        const productCode = document.getElementById('productCode').value.trim();
        const customerNameValue = document.getElementById('customerName').value.trim();
        // 根据客户名称获取客户ID
        const customerId = customerMap[customerNameValue] ? customerMap[customerNameValue].customerId : '';
        
        console.log('客户名称:', customerNameValue, '客户ID:', customerId, '映射表:', customerMap);  
        const productName = document.getElementById('productName').value.trim();
        const afterSaleDate = document.getElementById('afterSaleDate').value.trim();
        const repairReason = document.getElementById('repairReason').value.trim();
        const repairContent = document.getElementById('repairContent').value.trim();
        const repairVerificationResult = document.getElementById('repairVerificationResult').value.trim();
        const statusName = document.getElementById('statusName').value.trim();
        const remarks = document.getElementById('remarks').value.trim();

        const errors = [];

        if (!productCode) errors.push('模具编号');
        if (!customerNameValue) {
          errors.push('客户名称');
        } else if (!customerId) {
          errors.push('客户名称（请从下拉列表中选择有效的客户）');
        }
        if (!productName) errors.push('模具名称');
        if (!repairReason) errors.push('维修原因');

        if (errors.length > 0) {
          alert('以下字段必填:\n'+errors.join(','));
          return;
        }
      
      const productCodeValue = document.getElementById('productCode').value;
      const productId = mouldMap[productCodeValue] ? mouldMap[productCodeValue].productId : '';
      
      const data = {
        entity:{
          productCode: productCodeValue,
          productId: productId,
          customerId: customerId,
          productName: document.getElementById('productName').value,
          afterSaleDate: document.getElementById('afterSaleDate').value,
          repairReason: document.getElementById('repairReason').value,
          repairContent: document.getElementById('repairContent').value,
          repairVerificationResult: document.getElementById('repairVerificationResult').value,
          statusName: document.getElementById('statusName').value,
          remarks: document.getElementById('remarks').value,
          abnormalImages: imageState.abnormalImages,
          verifyImages: imageState.verifyImages,
        },
        field: [] 
      };
      
      if(editIndex) {
        data.entity.afterSaleId = editIndex;
        axios.post(`${VUE_APP_BASE_API}/crmAfterSale/update`, data,{ headers: { 'admin-token': adminToken } }).then(() => {
          localStorage.setItem('repairRecords', JSON.stringify(data));
          location.href = `list.html?adminToken=${adminToken}`;

       }); 
      } else {
        axios.post(`${VUE_APP_BASE_API}/crmAfterSale/add`, data,{ headers: { 'admin-token': adminToken } }).then(() => {
          localStorage.setItem('repairRecords', JSON.stringify(data));
          location.href = `list.html?adminToken=${adminToken}`;
       });

      } 
    }

    // 为模具编号添加change事件监听器
     document.getElementById('productCode').addEventListener('change', function() {
       const selectedProductCode = this.value;
       const productNameField = document.getElementById('productName');
       
       if (selectedProductCode && mouldMap[selectedProductCode]) {
         productNameField.value = mouldMap[selectedProductCode].name;
       }else{
         productNameField.value = '';
       }
     });

    handleImagePreview('abnormalImages', 'abnormalPreview', 'abnormalImages');
    handleImagePreview('verifyImages', 'verifyPreview', 'verifyImages');
  </script>
</body>
</html>
