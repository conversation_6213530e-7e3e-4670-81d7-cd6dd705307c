<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>模具维修记录列表</title>
  <style>
    body {
      font-family: sans-serif;
      background: #f4f6f8;
      padding: 8px;
      margin: 0;
    }

    * {
      box-sizing: border-box;
    }

    .search-container {
      background: white;
      padding: 8px;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .search-form {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .btn-search,
    .btn-reset {
      padding: 6px 12px;
      border-radius: 4px;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 14px;
    }

    .btn-search {
      background: #409eff;
    }

    .btn-reset {
      background: #909399;
    }

    .pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
      gap: 12px;
      font-size: 14px;
    }

    .title-content {
      text-align: center;
      color: #888;
      margin-top: 20px;
    }

    .mold-card {
      background: white;
      margin-bottom: 12px;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .mold-header {
      padding: 12px;
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      font-weight: bold;
      background: #f5f7fa;
      cursor: pointer;
    }

    .mold-body {
      padding: 12px;
      display: none;
      font-size: 14px;
      background: #fff;
    }

    .mold-body.expanded {
      display: block;
    }

    .mold-row {
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .btn-edit,
    .btn-delete {
      padding: 4px 8px;
      font-size: 13px;
      border-radius: 4px;
      margin-right: 6px;
      color: white;
      border: none;
      cursor: pointer;
    }

    .btn-edit {
      background: #409eff;
    }

    .btn-delete {
      background: #f56c6c;
    }
  </style>
</head>

<body>
  <div class="search-container">
    <div class="search-form">
      <input type="text" id="search-input" class="search-input" placeholder="请输入模具编号..." />
      <button class="btn-reset" onclick="handleSearch()">搜索</button>
      <button class="btn-search" onclick="handleAdd()">新增</button>
    </div>
  </div>

  <div id="table-container"></div>

  <div class="pagination" id="pagination" style="display: none;">
    <button onclick="goPage('prev')">上一页</button>
    <span id="page-info">第 1 / 1 页</span>
    <button onclick="goPage('next')">下一页</button>
  </div>

  <script src="https://crm.superband.com.cn:46443/axios.min.js"></script>
  <script>
    const VUE_APP_BASE_API = 'https://crm.superband.com.cn:46443';
    const params = new URLSearchParams(window.location.search);
    const adminToken = params.get('adminToken');
    let currentPage = 1;
    const limit = 15;
    let totalPages = 1;
    let currentSearch = '';
    let lastExpandedBody = null;
    const detailPagination = {};

    function fetchRepairRecords(page = 1, search = '') {
      const params = {
        page,
        limit,
        type: 20,
        groupFields: ['productCode', 'productName']
      };
      if (search) {
        params.search = search;
      }

      return axios.post(`${VUE_APP_BASE_API}/crmAfterSale/queryPageList`,
        params, { headers: { 'admin-token': adminToken } })
        .then(res => {
          const result = res?.data?.data;
          totalPages = Math.ceil(result && result.totalRow / limit);
          return result.list || [];
        })
        .catch(err => {
          console.log('err', err);
          console.error('请求失败:', err);
          return [];
        });
    }

    function renderTable(records) {
      const listContainer = document.getElementById('table-container');
      listContainer.innerHTML = '';

      if (records.length === 0) {
        listContainer.innerHTML = '<p class="title-content">暂无维修记录</p>';
        return;
      }

      records.reverse().forEach((item) => {
        const card = document.createElement('div');
        card.className = 'mold-card';

        const header = document.createElement('div');
        header.className = 'mold-header';
        header.innerHTML = `
          <div>
            <div>模具编号：${item.productCode}</div>
            <div style="font-weight: normal;">模具名称：${item.productName}</div>
            <span style="font-weight: normal;">维修次数：${item.count || 0}</span>
          </div>
          <div>
            <span style="margin-left: 12px; color: #409EFF;" class="arrow-icon">▼</span>
          </div>
        `;

        const body = document.createElement('div');
        body.className = 'mold-body';
        body.innerHTML = '<p>加载中...</p>';

        header.addEventListener('click', () => {
          const isExpanded = body.classList.contains('expanded');

          if (isExpanded) {
            body.classList.remove('expanded');
            updateArrowIcon(header, false);
            lastExpandedBody = null;
            return;
          }

          if (lastExpandedBody && lastExpandedBody !== body) {
            lastExpandedBody.classList.remove('expanded');
            updateArrowIcon(lastExpandedBody.previousElementSibling, false);
          }

          body.classList.add('expanded');
          updateArrowIcon(header, true);
          lastExpandedBody = body;

          if (!body.dataset.loaded) {
            fetchDetailList(item.productCode,item.productName, body);
            body.dataset.loaded = 'true';
          }
        });

        card.appendChild(header);
        card.appendChild(body);
        listContainer.appendChild(card);
      });

      updatePagination();
    }

    function updateArrowIcon(headerEl, expanded) {
      const icon = headerEl.querySelector('.arrow-icon');
      if (icon) icon.textContent = expanded ? '▲' : '▼';
    }

    function fetchDetailList(productCode,productName, container,page = 1) {
      const pageSize = 5;
      axios.post(`${VUE_APP_BASE_API}/crmAfterSale/queryPageList`, {
        type: 20,
        limit: pageSize,
        page,
        searchList: [{
          name: "productCode",
          type: "1",
          values: [productCode]
        },
        {
            name: "productName",
            type: "1",
            values: [productName]
          }]
      }, { headers: { 'admin-token': adminToken} })
        .then(res => {
          const list = res.data.data.list || [];
          const totalPage = Math.ceil(res.data.data.totalRow / pageSize);

          detailPagination[productCode] = { page, total: totalPage };
          if (list.length === 0) {
            container.innerHTML = '<p class="mold-row">暂无维修记录</p>';
            return;
          }

          const html = list.map(item => `
            <div class="mold-row">客户名称：${item.customerName || ''}
              <span style="float:right;">
                <button class="btn-edit" onclick="editRecord('${item.afterSaleId}')">编辑</button>
                <button class="btn-delete" onclick="deleteRecord('${item.afterSaleId}')">删除</button></span>
              </div>
            <div class="mold-row">维修日期：${item.afterSaleDate || '-'}</div>
            <div class="mold-row">维修状态：${item.statusName || ''}</div>
            <div class="mold-row">维修原因：${item.repairReason || ''}</div>
            <div class="mold-row">维修内容：${item.repairContent || ''}</div>
            <div class="mold-row">维修验证结果：${item.repairVerificationResult || ''}</div>
            <div class="mold-row">备注：${item.remarks || ''}</div>
            <hr style="margin: 8px 0;" />
          `).join('');
          const paginationControls = `
          <div class="pagination" style="margin-top: 8px;">
            <button onclick="changeSubPage('${productCode}', 'prev')">上一页</button>
            <span style="margin: 0 8px;">第 ${page} / ${totalPage} 页</span>
            <button onclick="changeSubPage('${productCode}', 'next')">下一页</button>
          </div>
        `;
          container.innerHTML = html + paginationControls;
        })
        .catch(err => {
          container.innerHTML = '<p class="mold-row">加载失败</p>';
          console.error(err);
        });
    }
    function changeSubPage(productCode, direction) {
      const info = detailPagination[productCode];
      if (!info) return;

      let newPage = info.page;
      if (direction === 'prev' && newPage > 1) newPage--;
      else if (direction === 'next' && newPage < info.total) newPage++;
      else return;

      const body = document.querySelector(`.mold-body[data-code="${productCode}"]`);
      if (body) fetchDetailList(productCode, productName, body, newPage);
    }


    function editRecord(id) {
      location.href = `repair.html?edit=${id}&adminToken=${adminToken}`;
    }

    function deleteRecord(id) {
      if (confirm('确定删除该维修记录？')) {
        axios.post(`${VUE_APP_BASE_API}/crmAfterSale/deleteByIds`,
          [id], { headers: { 'admin-token': adminToken } })
          .then(() => {
            fetchRepairRecords(currentPage, currentSearch).then(renderTable);
          })
          .catch(err => {
            alert('删除失败');
            console.error(err);
          });
      }
    }

    function handleSearch() {
      currentSearch = document.getElementById('search-input').value.trim();
      currentPage = 1;
      fetchRepairRecords(currentPage, currentSearch).then(renderTable);
    }

    function handleAdd() {
      location.href = `repair.html?adminToken=${adminToken}`;
    }

    function goPage(direction) {
      if (direction === 'prev' && currentPage > 1) currentPage--;
      else if (direction === 'next' && currentPage < totalPages) currentPage++;
      else return;

      fetchRepairRecords(currentPage, currentSearch).then(renderTable);
    }

    function updatePagination() {
      document.getElementById('pagination').style.display = 'flex';
      document.getElementById('page-info').textContent = `第 ${currentPage} / ${totalPages} 页`;
    }

    document.addEventListener('DOMContentLoaded', function () {
      document.getElementById('search-input').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') handleSearch();
      });

      fetchRepairRecords(currentPage).then(renderTable);
    });
  </script>
</body>

</html>
